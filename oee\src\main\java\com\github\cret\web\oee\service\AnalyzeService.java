package com.github.cret.web.oee.service;

import java.util.List;

import com.github.cret.web.oee.domain.analyze.AlarmInfo;
import com.github.cret.web.oee.domain.analyze.AnalyzeQuery;
import com.github.cret.web.oee.domain.analyze.AnalyzeResult;
import com.github.cret.web.oee.domain.analyze.HourlyOutputList;
import com.github.cret.web.oee.domain.analyze.OeeResult;
import com.github.cret.web.oee.domain.theoreticalOutput.TheoreticalOutputSample;

/**
 * 分析服务接口
 */
public interface AnalyzeService {

	/**
	 * 查询产线一段时间内的线体OEE
	 * @param query
	 * @return
	 */
	public AnalyzeResult getLineOee(AnalyzeQuery query);

	/**
	 * 获取线体每小时产出数据
	 * @param query
	 * @return
	 */
	public HourlyOutputList getHourlyOutput(AnalyzeQuery query);

	/**
	 * 获取Top5异常统计数据
	 * @param query
	 * @return
	 */
	public List<AlarmInfo> getTopAlarms(AnalyzeQuery query);

	/**
	 * 根据车间编码获取每条线体的单月OEE数据
	 * @param query
	 * @return
	 */
	public List<OeeResult> getWorkshopMonthlyOee(String workShopId, AnalyzeQuery query);

	/**
	 * 根据车间编码获取单日每条线体的OEE数据
	 */
	public List<OeeResult> getWorkshopDailyOee(String workShopId, AnalyzeQuery query);

	/**
	 * 获取为样品的理论ct。
	 * @param lineCode
	 * @param productModel
	 * @return
	 */
	public List<TheoreticalOutputSample> getTheoreticalOutputSamples(String lineCode, String productModel);

}